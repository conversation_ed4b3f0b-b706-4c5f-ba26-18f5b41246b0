<template>
    <BuildLayout title="Build your campaign" :auth="auth" :currentStep="0">
        <div class="container space-y-16">
            <AccordionDropdown
                showCheck
                :isComplete="hasSelectedCampaignType"
                :isOpen="activeAccordion === 'campaignType'"
                @toggle="
                    activeAccordion =
                        activeAccordion === 'campaignType'
                            ? null
                            : 'campaignType'
                "
            >
                <template #header>
                    <IconOne />
                    What kind of campaign would you like to run
                </template>
                <template #body>
                    <div
                        class="grid grid-cols-1 gap-16 md:grid-cols-2 lg:grid-cols-4"
                    >
                        <SelectableOptionCard
                            v-model="selectedCampaignType"
                            value="emergency"
                            title="Run an emergency appeal"
                        >
                            <IconEmergency />
                        </SelectableOptionCard>

                        <SelectableOptionCard
                            v-model="selectedCampaignType"
                            value="campaign"
                            title="Promote a campaign"
                        >
                            <IconTarget />
                        </SelectableOptionCard>

                        <SelectableOptionCard
                            v-model="selectedCampaignType"
                            value="event"
                            title="Promote an event"
                        >
                            <IconCalendarStar />
                        </SelectableOptionCard>

                        <SelectableOptionCard
                            v-model="selectedCampaignType"
                            value="brand"
                            title="Create a brand awareness campaign"
                        >
                            <IconSpeakerphone />
                        </SelectableOptionCard>
                    </div>
                </template>
            </AccordionDropdown>

            <AccordionDropdown
                v-if="selectedCampaignType !== 'brand'"
                showCheck
                :isComplete="hasTargetRaiseAmount"
                :isOpen="activeAccordion === 'targetAmount'"
                @toggle="
                    activeAccordion =
                        activeAccordion === 'targetAmount'
                            ? null
                            : 'targetAmount'
                "
            >
                <template #header>
                    <IconTwo />
                    How much are you trying to raise
                </template>
                <template #body>
                    <div>
                        <div @keydown.enter="handleTargetAmountNext">
                            <TextInput
                                ref="targetAmountInput"
                                isPrice
                                v-model.number="targetRaiseAmount"
                            />
                        </div>
                        <div class="flex justify-end">
                            <Button
                                class="flex items-center justify-center gap-6"
                                @click="handleTargetAmountNext"
                                :disabled="!hasTargetRaiseAmount"
                            >
                                <span>Next</span>
                                <IconArrowRightBold
                                    :class="
                                        hasTargetRaiseAmount
                                            ? 'stroke-text-body'
                                            : 'stroke-text-disabled'
                                    "
                                />
                            </Button>
                        </div>
                    </div>
                </template>
            </AccordionDropdown>

            <AccordionDropdown
                showCheck
                :isComplete="hasCampaignDescription"
                :isOpen="activeAccordion === 'description'"
                @toggle="
                    activeAccordion =
                        activeAccordion === 'description' ? null : 'description'
                "
            >
                <template #header>
                    <IconTwo v-if="selectedCampaignType === 'brand'" />

                    <IconThree v-else />
                    What would you like people to do
                </template>
                <template #body>
                    <div>
                        <div @keydown.enter="handleDescriptionNext">
                            <TextArea
                                ref="descriptionInput"
                                rows="6"
                                v-model="campaignDescription"
                                label="We're organising a fun run, I'd like people to sign up at the link in my bio..."
                            />
                        </div>
                        <div class="flex justify-end">
                            <Button
                                class="flex items-center justify-center gap-6"
                                @click="handleDescriptionNext"
                                :disabled="!hasCampaignDescription"
                            >
                                <span>Next</span>
                                <IconArrowRightBold
                                    :class="
                                        hasCampaignDescription
                                            ? 'stroke-text-body'
                                            : 'stroke-text-disabled'
                                    "
                                />
                            </Button>
                        </div>
                    </div>
                </template>
            </AccordionDropdown>
        </div>

        <div class="container my-64 flex justify-end">
            <button
                ref="createAudienceButton"
                @click.prevent="handleNext"
                class="flex items-center gap-4 py-8 font-header text-lg font-bold transition-all duration-200"
                :class="
                    canContinue
                        ? 'text-text-body focus:ring-2 focus:ring-text-action focus:ring-offset-2'
                        : 'text-text-disabled'
                "
                :disabled="!canContinue"
            >
                Create Audience

                <IconArrowRightBold
                    :class="
                        canContinue
                            ? 'stroke-text-body'
                            : 'stroke-text-disabled'
                    "
                />
            </button>
        </div>
    </BuildLayout>
</template>

<script setup>
import AccordionDropdown from "@/Components/AccordionDropdown/AccordionDropdown.vue";
import IconOne from "@/Components/Icons/NumberSquares/IconOne.vue";
import IconThree from "@/Components/Icons/NumberSquares/IconThree.vue";
import IconTwo from "@/Components/Icons/NumberSquares/IconTwo.vue";
import BuildLayout from "@/Layouts/BuildLayout.vue";
import { ref, computed, watch, nextTick } from "vue";
import SelectableOptionCard from "@/Components/SelectableOptionCard/SelectableOptionCard.vue";
import IconEmergency from "@/Components/Icons/IconEmergency.vue";
import IconCalendarStar from "@/Components/Icons/IconCalendarStar.vue";
import IconSpeakerphone from "@/Components/Icons/IconSpeakerphone.vue";
import IconTarget from "@/Components/Icons/IconTarget.vue";
import TextInput from "@/Components/TextInput/TextInput.vue";
import TextArea from "@/Components/TextArea/TextArea.vue";
import { router } from "@inertiajs/vue3";
import IconArrowRightBold from "@/Components/Icons/IconArrowRightBold.vue";
import Button from "@/Components/Button/Button.vue";

import { useFindDonorsStore } from "@/stores/findDonors";
import { storeToRefs } from "pinia";

const findDonors = useFindDonorsStore();
const { selectedCampaignType, targetRaiseAmount, campaignDescription } =
    storeToRefs(findDonors);

defineProps({
    auth: Object,
});

const activeAccordion = ref("campaignType"); // Start with first accordion open
const createAudienceButton = ref(null); // Reference to the Create Audience button
const targetAmountInput = ref(null); // Reference to the target amount input
const descriptionInput = ref(null); // Reference to the description input

const hasSelectedCampaignType = computed(
    () =>
        selectedCampaignType.value !== "" &&
        selectedCampaignType.value !== null,
);

const hasTargetRaiseAmount = computed(
    () => targetRaiseAmount.value !== "" && targetRaiseAmount.value !== null,
);

const hasCampaignDescription = computed(() => campaignDescription.value !== "");

const canContinue = computed(() => {
    if (selectedCampaignType.value === "brand") {
        return hasSelectedCampaignType.value && hasCampaignDescription.value;
    } else {
        return (
            hasSelectedCampaignType.value &&
            hasTargetRaiseAmount.value &&
            hasCampaignDescription.value
        );
    }
});

// Automatic progression logic - only for campaign type selection
watch(hasSelectedCampaignType, async (isComplete) => {
    if (isComplete && activeAccordion.value === "campaignType") {
        // Move to next accordion based on campaign type
        if (selectedCampaignType.value === "brand") {
            activeAccordion.value = "description";
            // Focus on description input after accordion opens
            await nextTick();
            if (descriptionInput.value) {
                descriptionInput.value.focus();
            }
        } else {
            activeAccordion.value = "targetAmount";
            // Focus on target amount input after accordion opens
            await nextTick();
            if (targetAmountInput.value) {
                targetAmountInput.value.focus();
            }
        }
    }
});

// Handler functions for manual progression
const handleTargetAmountNext = async () => {
    if (hasTargetRaiseAmount.value) {
        activeAccordion.value = "description";
        // Focus on description input after accordion opens
        await nextTick();
        if (descriptionInput.value) {
            descriptionInput.value.focus();
        }
    }
};

const handleDescriptionNext = async () => {
    if (hasCampaignDescription.value) {
        // Close the last accordion when complete
        activeAccordion.value = null;
    }
};

const handleNext = () => {
    if (canContinue) {
        router.get("/build/audience");
    }
};
</script>

<template>
    <div
        class="flex h-full flex-col overflow-hidden rounded-xl border border-[#DCDDDD] bg-white shadow-[0px_4px_4px_0px_rgba(0,0,0,0.25)]"
    >
        <!-- Header with Platform Tabs -->
        <div
            class="flex-shrink-0 border-b border-[#DCDDDD] bg-[#F8F9FA] px-24 py-16"
        >
            <!-- Platform Tabs -->
            <div class="flex gap-8 overflow-x-auto">
                <Button @click="handleBackNavigation" :title="backButtonTitle">
                    <VsxIcon iconName="ArrowLeft" :size="18" type="linear" />
                </Button>
                <div
                    v-for="(count, platformId) in platformsData"
                    :key="platformId"
                    class="relative flex flex-shrink-0 cursor-pointer items-center gap-8 rounded-lg px-16 py-8 transition-all duration-200"
                    :class="
                        activeTab === platformId
                            ? 'border-2 border-[#FF5EAB] bg-white shadow-sm'
                            : 'border border-[#E5E7EB] bg-white/50 hover:border-[#FF5EAB]/30 hover:bg-white'
                    "
                    @click="setActiveTab(platformId)"
                >
                    <div
                        class="relative flex h-24 w-24 items-center justify-center rounded-full"
                        :style="{
                            backgroundColor:
                                getPlatformColor(platformId) + '20',
                        }"
                    >
                        <VsxIcon
                            :iconName="getPlatformIcon(platformId)"
                            :size="16"
                            :color="getPlatformColor(platformId)"
                            type="linear"
                        />

                        <!-- Status indicator -->
                        <div
                            v-if="
                                getPlatformStatus(platformId) === 'generating'
                            "
                            class="absolute -right-1 -top-1 flex h-6 w-6 items-center justify-center rounded-full bg-[#FF5EAB]"
                        >
                            <div
                                class="h-3 w-3 animate-pulse rounded-full bg-white"
                            ></div>
                        </div>
                        <div
                            v-else-if="
                                getPlatformStatus(platformId) === 'complete'
                            "
                            class="bg-green-500 absolute -right-1 -top-1 flex h-6 w-6 items-center justify-center rounded-full"
                        >
                            <VsxIcon
                                iconName="TickCircle"
                                :size="12"
                                color="white"
                                type="linear"
                            />
                        </div>
                    </div>
                    <div class="flex flex-col">
                        <span class="text-sm font-medium text-text-body">{{
                            getPlatformName(platformId)
                        }}</span>
                        <span class="text-text-secondary text-xs"
                            >{{ count }} post{{ count > 1 ? "s" : "" }}</span
                        >
                    </div>
                </div>
            </div>
        </div>

        <!-- Chat Interface -->
        <div class="flex min-h-0 flex-1 flex-col">
            <!-- Chat Header -->
            <div
                class="flex flex-shrink-0 items-center justify-between border-b border-[#DCDDDD] bg-[#FAFBFC] p-16"
            >
                <div class="flex items-center gap-8">
                    <div
                        class="flex h-32 w-32 items-center justify-center rounded-full"
                        :style="{
                            backgroundColor: getPlatformColor(activeTab) + '20',
                        }"
                    >
                        <VsxIcon
                            :iconName="getPlatformIcon(activeTab)"
                            :size="20"
                            :color="getPlatformColor(activeTab)"
                            type="linear"
                        />
                    </div>
                    <div>
                        <h4 class="font-medium text-text-body">
                            {{ getPlatformName(activeTab) }}
                        </h4>
                        <p class="text-text-secondary text-xs">
                            {{ headerSubtitle }}
                        </p>
                    </div>
                </div>
                <div class="flex items-center gap-12">
                    <span class="text-text-secondary text-xs">{{
                        currentDate
                    }}</span>
                    <Button
                        @click="handleSchedulePost"
                        color="action"
                        size="md"
                        class="flex items-center gap-6 px-16 py-8 text-sm font-semibold shadow-sm"
                    >
                        <VsxIcon iconName="Calendar" :size="16" type="linear" />
                        <span>{{ scheduleButtonText }}</span>
                    </Button>
                </div>
            </div>

            <!-- Chat Messages -->
            <div
                class="min-h-0 flex-1 overflow-y-auto p-16"
                ref="chatContainer"
            >
                <div
                    v-if="
                        getTabMessages(activeTab).length === 0 &&
                        !isTabStreaming
                    "
                    class="flex h-full flex-col items-center justify-center text-center"
                >
                    <div class="mb-16">
                        <VsxIcon
                            :iconName="getPlatformIcon(activeTab)"
                            :size="48"
                            :color="getPlatformColor(activeTab)"
                            type="linear"
                            class="opacity-30"
                        />
                    </div>
                    <h5 class="mb-8 font-medium text-text-body">
                        {{ getPlatformName(activeTab) }} {{ contentTypeLabel }}
                    </h5>
                    <p class="text-text-secondary mb-16 max-w-[400px] text-sm">
                        {{ getEmptyStateMessage() }}
                    </p>
                    <div
                        v-if="isLoading || isAutoGenerating"
                        class="flex items-center gap-8"
                    >
                        <div
                            class="h-16 w-16 animate-spin rounded-full border-b-2 border-[#FF5EAB]"
                        ></div>
                        <span class="text-gray-600 text-sm">{{
                            loadingMessage
                        }}</span>
                    </div>

                    <!-- Manual generation option (only show if automatic generation is complete and mode is posts) -->
                    <div
                        v-else-if="
                            !isAutoGenerating && !isLoading && mode === 'posts'
                        "
                        class="mt-8"
                    >
                        <Button
                            size="sm"
                            variant="outline"
                            @click="generateContentForPlatform(activeTab)"
                            class="text-xs"
                        >
                            <VsxIcon
                                iconName="Refresh"
                                :size="14"
                                type="linear"
                            />
                            <span class="ml-4">Generate New Content</span>
                        </Button>
                    </div>
                </div>

                <!-- Content Display -->
                <div v-else class="space-y-16">
                    <!-- Posts Mode: Complex UI with PostCard components -->
                    <template v-if="mode === 'posts'">
                        <div
                            v-for="message in getTabMessages(activeTab)"
                            :key="message.id"
                            class="flex flex-col gap-8"
                            :class="
                                message.role === 'user'
                                    ? 'items-end'
                                    : 'items-start'
                            "
                        >
                            <div
                                v-if="message.role === 'user'"
                                class="max-w-[80%] rounded-[16px_16px_4px_16px] bg-[#FF5EAB] px-16 py-12 text-white"
                            >
                                <p class="text-sm">{{ message.content }}</p>
                            </div>

                            <div v-else class="w-full">
                                <!-- Check if this is a completed post message (single or multiple posts) -->
                                <div v-if="message.isPost" class="space-y-12">
                                    <!-- Individual Post Cards -->
                                    <div class="space-y-12">
                                        <PostCard
                                            v-for="(
                                                post, index
                                            ) in getIndividualPosts(
                                                message.content,
                                            )"
                                            :key="`${message.id}-post-${index}`"
                                            :postContent="post.content"
                                            :postHtmlContent="post.htmlContent"
                                            :postIndex="index"
                                            :totalPosts="
                                                getPostCount(message.content)
                                            "
                                            :platformId="activeTab"
                                            :platformName="
                                                getPlatformName(activeTab)
                                            "
                                            :platformIcon="
                                                getPlatformIcon(activeTab)
                                            "
                                            :platformColor="
                                                getPlatformColor(activeTab)
                                            "
                                            :postMedia="
                                                getPostMedia(message.id, index)
                                            "
                                            :isLoading="
                                                isLoading || isTabStreaming
                                            "
                                            :isAnyMediaProcessActive="
                                                isAnyMediaProcessActive
                                            "
                                            :isEdited="post.isEdited"
                                            :editedAt="post.editedAt"
                                            @update-post="
                                                handleUpdatePost(
                                                    message.id,
                                                    $event,
                                                )
                                            "
                                            @edit-post="
                                                handleEditPost(
                                                    message.id,
                                                    $event,
                                                )
                                            "
                                            @duplicate-post="
                                                handleDuplicatePost(
                                                    message.id,
                                                    $event,
                                                )
                                            "
                                            @delete-post="
                                                handleDeletePost(
                                                    message.id,
                                                    $event,
                                                )
                                            "
                                            @generate-image="
                                                handleGeneratePostImage(
                                                    message.id,
                                                    $event,
                                                )
                                            "
                                            @generate-video="
                                                handleGeneratePostVideo(
                                                    message.id,
                                                    $event,
                                                )
                                            "
                                            @renew-image="
                                                handleRenewPostImage(
                                                    message.id,
                                                    $event,
                                                )
                                            "
                                            @renew-video="
                                                handleRenewPostVideo(
                                                    message.id,
                                                    $event,
                                                )
                                            "
                                            @refresh-media="handleRefreshMedia"
                                            @cancel-media="handleCancelMedia"
                                        />
                                    </div>
                                </div>

                                <!-- Single post or non-post message (original layout) -->
                                <div
                                    v-else
                                    class="w-full max-w-[90%] rounded-[16px_4px_16px_16px] bg-[#F8F9FA] px-16 py-12"
                                >
                                    <!-- Inline editing mode -->
                                    <div
                                        v-if="editingMessageId === message.id"
                                        class="space-y-12"
                                    >
                                        <textarea
                                            v-model="editingContent"
                                            @keydown="handleInlineEditKeyDown"
                                            class="min-h-[80px] w-full resize-none rounded-lg border-0 bg-white px-12 py-8 text-sm leading-relaxed outline-none"
                                            placeholder="Edit your content..."
                                            rows="3"
                                        ></textarea>
                                        <div class="flex items-center gap-8">
                                            <Button
                                                size="sm"
                                                color="action"
                                                @click="saveInlineEdit"
                                            >
                                                Save
                                            </Button>
                                            <Button
                                                size="sm"
                                                variant="outline"
                                                @click="cancelInlineEdit"
                                            >
                                                Cancel
                                            </Button>
                                        </div>
                                    </div>
                                    <!-- Normal display mode -->
                                    <div v-else>
                                        <div
                                            v-if="message.htmlContent"
                                            class="text-gray-700 hover:bg-gray-50 prose prose-sm max-w-none cursor-pointer rounded p-8 transition-colors"
                                            @click="startInlineEdit(message)"
                                            v-html="message.htmlContent"
                                        ></div>
                                        <p
                                            v-else
                                            class="text-gray-700 hover:bg-gray-50 cursor-pointer rounded p-8 text-sm transition-colors"
                                            @click="startInlineEdit(message)"
                                        >
                                            {{ message.content }}
                                        </p>
                                        <div
                                            class="text-gray-400 mt-4 text-xs opacity-0 transition-opacity hover:opacity-100"
                                        >
                                            Click to edit
                                        </div>
                                    </div>

                                    <!-- Edited indicator -->
                                    <div
                                        v-if="message.isEdited"
                                        class="text-gray-500 mt-8 flex items-center gap-4 text-xs italic"
                                    >
                                        <VsxIcon
                                            iconName="Edit"
                                            :size="12"
                                            type="linear"
                                        />
                                        <span
                                            >Edited {{ message.editedAt }}</span
                                        >
                                    </div>

                                    <div
                                        v-if="message.isError"
                                        class="text-12 text-red-600 mt-8 font-medium"
                                    >
                                        ⚠️ Error occurred
                                    </div>

                                    <!-- Generated Media Display -->
                                    <div
                                        v-if="
                                            getMessageMedia(message.id).length >
                                            0
                                        "
                                        class="mt-12 pt-12"
                                    >
                                        <GeneratedMedia
                                            v-for="mediaItem in getMessageMedia(
                                                message.id,
                                            )"
                                            :key="mediaItem.id"
                                            :mediaItem="mediaItem"
                                            @refresh="handleRefreshMedia"
                                            @cancel="handleCancelMedia"
                                        />
                                    </div>

                                    <!-- Action Buttons -->
                                    <div
                                        class="mt-12 flex items-center justify-between pt-8"
                                    >
                                        <!-- Left side: Edit button -->
                                        <div class="flex items-center gap-8">
                                            <button
                                                v-if="message.isPost"
                                                @click="
                                                    handleEditContent(message)
                                                "
                                                :disabled="
                                                    isLoading ||
                                                    isTabStreaming ||
                                                    isAnyMediaProcessActive
                                                "
                                                class="text-gray-600 hover:bg-pink-50 flex h-28 w-28 items-center justify-center rounded-lg transition-colors hover:text-[#FF5EAB] disabled:cursor-not-allowed disabled:opacity-50"
                                                title="Edit post content"
                                            >
                                                <VsxIcon
                                                    iconName="Edit"
                                                    :size="16"
                                                    type="linear"
                                                />
                                            </button>
                                        </div>

                                        <!-- Right side: Media generation buttons -->
                                        <div class="flex items-center gap-8">
                                            <template
                                                v-if="
                                                    hasCompletedMedia(
                                                        getMessageMedia(
                                                            message.id,
                                                        ),
                                                        'image',
                                                    )
                                                "
                                            >
                                                <button
                                                    @click="
                                                        handleRenewImage(
                                                            message,
                                                        )
                                                    "
                                                    :disabled="
                                                        isAnyMediaProcessActive
                                                    "
                                                    class="text-12 text-gray-600 hover:bg-pink-50 flex items-center gap-4 rounded-lg px-8 py-4 font-medium transition-colors hover:text-[#FF5EAB] disabled:cursor-not-allowed disabled:opacity-50"
                                                    title="Regenerate images for this content"
                                                >
                                                    <VsxIcon
                                                        iconName="Refresh"
                                                        :size="16"
                                                        type="linear"
                                                    />
                                                    <span>Renew Images</span>
                                                </button>
                                            </template>
                                            <template v-else>
                                                <button
                                                    @click="
                                                        handleGenerateImage(
                                                            message,
                                                        )
                                                    "
                                                    :disabled="
                                                        isAnyMediaProcessActive
                                                    "
                                                    class="text-12 text-gray-600 hover:bg-pink-50 flex items-center gap-4 rounded-lg px-8 py-4 font-medium transition-colors hover:text-[#FF5EAB] disabled:cursor-not-allowed disabled:opacity-50"
                                                    title="Generate image for this content"
                                                >
                                                    <VsxIcon
                                                        iconName="Gallery"
                                                        :size="16"
                                                        type="linear"
                                                    />
                                                    <span>Generate Image</span>
                                                </button>
                                            </template>

                                            <template
                                                v-if="
                                                    hasCompletedMedia(
                                                        getMessageMedia(
                                                            message.id,
                                                        ),
                                                        'video',
                                                    )
                                                "
                                            >
                                                <button
                                                    @click="
                                                        handleRenewVideo(
                                                            message,
                                                        )
                                                    "
                                                    :disabled="
                                                        isAnyMediaProcessActive
                                                    "
                                                    class="text-12 text-gray-600 hover:bg-pink-50 flex items-center gap-4 rounded-lg px-8 py-4 font-medium transition-colors hover:text-[#FF5EAB] disabled:cursor-not-allowed disabled:opacity-50"
                                                    title="Regenerate videos for this content"
                                                >
                                                    <VsxIcon
                                                        iconName="Refresh"
                                                        :size="16"
                                                        type="linear"
                                                    />
                                                    <span>Renew Videos</span>
                                                </button>
                                            </template>
                                            <template v-else>
                                                <button
                                                    @click="
                                                        handleGenerateVideo(
                                                            message,
                                                        )
                                                    "
                                                    :disabled="
                                                        isAnyMediaProcessActive
                                                    "
                                                    class="text-12 text-gray-600 hover:bg-pink-50 flex items-center gap-4 rounded-lg px-8 py-4 font-medium transition-colors hover:text-[#FF5EAB] disabled:cursor-not-allowed disabled:opacity-50"
                                                    title="Generate video for this content"
                                                >
                                                    <VsxIcon
                                                        iconName="VideoPlay"
                                                        :size="16"
                                                        type="linear"
                                                    />
                                                    <span>Generate Video</span>
                                                </button>
                                            </template>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>

                    <!-- Campaign Mode: Use PostCard components for consistency -->
                    <template v-else>
                        <div
                            v-for="message in getTabMessages(activeTab)"
                            :key="message.id"
                            class="flex flex-col gap-8"
                            :class="
                                message.role === 'user'
                                    ? 'items-end'
                                    : 'items-start'
                            "
                        >
                            <div
                                v-if="message.role === 'user'"
                                class="max-w-[80%] rounded-[16px_16px_4px_16px] bg-[#FF5EAB] px-16 py-12 text-white"
                            >
                                <p class="text-sm">{{ message.content }}</p>
                            </div>

                            <div v-else class="w-full">
                                <!-- Check if this is a completed campaign content message (single or multiple posts) -->
                                <div v-if="message.isPost" class="space-y-12">
                                    <!-- Individual Campaign Content Cards using PostCard -->
                                    <div class="space-y-12">
                                        <PostCard
                                            v-for="(
                                                post, index
                                            ) in getIndividualPosts(
                                                message.content,
                                            )"
                                            :key="`${message.id}-post-${index}`"
                                            :postContent="post.content"
                                            :postHtmlContent="post.htmlContent"
                                            :postIndex="index"
                                            :totalPosts="
                                                getPostCount(message.content)
                                            "
                                            :platformId="activeTab"
                                            :platformName="
                                                getPlatformName(activeTab)
                                            "
                                            :platformIcon="
                                                getPlatformIcon(activeTab)
                                            "
                                            :platformColor="
                                                getPlatformColor(activeTab)
                                            "
                                            :postMedia="
                                                getPostMedia(message.id, index)
                                            "
                                            :isLoading="
                                                isLoading || isTabStreaming
                                            "
                                            :isAnyMediaProcessActive="
                                                isAnyMediaProcessActive
                                            "
                                            :isEdited="post.isEdited"
                                            :editedAt="post.editedAt"
                                            @update-post="
                                                handleUpdatePost(
                                                    message.id,
                                                    $event,
                                                )
                                            "
                                            @edit-post="
                                                handleEditPost(
                                                    message.id,
                                                    $event,
                                                )
                                            "
                                            @duplicate-post="
                                                handleDuplicatePost(
                                                    message.id,
                                                    $event,
                                                )
                                            "
                                            @delete-post="
                                                handleDeletePost(
                                                    message.id,
                                                    $event,
                                                )
                                            "
                                            @generate-image="
                                                handleGeneratePostImage(
                                                    message.id,
                                                    $event,
                                                )
                                            "
                                            @generate-video="
                                                handleGeneratePostVideo(
                                                    message.id,
                                                    $event,
                                                )
                                            "
                                            @renew-image="
                                                handleRenewPostImage(
                                                    message.id,
                                                    $event,
                                                )
                                            "
                                            @renew-video="
                                                handleRenewPostVideo(
                                                    message.id,
                                                    $event,
                                                )
                                            "
                                            @refresh-media="handleRefreshMedia"
                                            @cancel-media="handleCancelMedia"
                                        />
                                    </div>
                                </div>

                                <!-- Single campaign content or non-post message (original layout) -->
                                <div
                                    v-else
                                    class="w-full max-w-[90%] rounded-[16px_4px_16px_16px] bg-[#F8F9FA] px-16 py-12"
                                >
                                    <!-- Inline editing mode -->
                                    <div
                                        v-if="editingMessageId === message.id"
                                        class="space-y-12"
                                    >
                                        <textarea
                                            v-model="editingContent"
                                            @keydown="handleInlineEditKeyDown"
                                            class="min-h-[80px] w-full resize-none rounded-lg border-0 bg-white px-12 py-8 text-sm leading-relaxed outline-none"
                                            placeholder="Edit your content..."
                                            rows="3"
                                        ></textarea>
                                        <div class="flex items-center gap-8">
                                            <Button
                                                size="sm"
                                                color="action"
                                                @click="saveInlineEdit"
                                            >
                                                Save
                                            </Button>
                                            <Button
                                                size="sm"
                                                variant="outline"
                                                @click="cancelInlineEdit"
                                            >
                                                Cancel
                                            </Button>
                                        </div>
                                    </div>
                                    <!-- Normal display mode -->
                                    <div v-else>
                                        <div
                                            v-if="message.htmlContent"
                                            class="text-gray-700 hover:bg-gray-50 prose prose-sm max-w-none cursor-pointer rounded p-8 transition-colors"
                                            @click="startInlineEdit(message)"
                                            v-html="message.htmlContent"
                                        ></div>
                                        <p
                                            v-else
                                            class="text-gray-700 hover:bg-gray-50 cursor-pointer rounded p-8 text-sm transition-colors"
                                            @click="startInlineEdit(message)"
                                        >
                                            {{ message.content }}
                                        </p>
                                        <div
                                            class="text-gray-400 mt-4 text-xs opacity-0 transition-opacity hover:opacity-100"
                                        >
                                            Click to edit
                                        </div>
                                    </div>

                                    <!-- Edited indicator -->
                                    <div
                                        v-if="message.isEdited"
                                        class="text-gray-500 mt-8 flex items-center gap-4 text-xs italic"
                                    >
                                        <VsxIcon
                                            iconName="Edit"
                                            :size="12"
                                            type="linear"
                                        />
                                        <span
                                            >Edited {{ message.editedAt }}</span
                                        >
                                    </div>

                                    <div
                                        v-if="message.isError"
                                        class="text-12 text-red-600 mt-8 font-medium"
                                    >
                                        ⚠️ Error occurred
                                    </div>

                                    <!-- Generated Media Display -->
                                    <div
                                        v-if="
                                            getMessageMedia(message.id).length >
                                            0
                                        "
                                        class="mt-12 pt-12"
                                    >
                                        <GeneratedMedia
                                            v-for="mediaItem in getMessageMedia(
                                                message.id,
                                            )"
                                            :key="mediaItem.id"
                                            :mediaItem="mediaItem"
                                            @refresh="handleRefreshMedia"
                                            @cancel="handleCancelMedia"
                                        />
                                    </div>

                                    <!-- Action Buttons -->
                                    <div
                                        class="mt-12 flex items-center justify-between pt-8"
                                    >
                                        <!-- Left side: Edit button -->
                                        <div class="flex items-center gap-8">
                                            <button
                                                v-if="message.isPost"
                                                @click="
                                                    handleEditContent(message)
                                                "
                                                :disabled="
                                                    isLoading ||
                                                    isTabStreaming ||
                                                    isAnyMediaProcessActive
                                                "
                                                class="text-gray-600 hover:bg-pink-50 flex h-28 w-28 items-center justify-center rounded-lg transition-colors hover:text-[#FF5EAB] disabled:cursor-not-allowed disabled:opacity-50"
                                                title="Edit campaign content"
                                            >
                                                <VsxIcon
                                                    iconName="Edit"
                                                    :size="16"
                                                    type="linear"
                                                />
                                            </button>
                                        </div>

                                        <!-- Right side: Media generation buttons -->
                                        <div class="flex items-center gap-8">
                                            <template
                                                v-if="
                                                    hasCompletedMedia(
                                                        getMessageMedia(
                                                            message.id,
                                                        ),
                                                        'image',
                                                    )
                                                "
                                            >
                                                <button
                                                    @click="
                                                        handleRenewImage(
                                                            message,
                                                        )
                                                    "
                                                    :disabled="
                                                        isAnyMediaProcessActive
                                                    "
                                                    class="text-12 text-gray-600 hover:bg-pink-50 flex items-center gap-4 rounded-lg px-8 py-4 font-medium transition-colors hover:text-[#FF5EAB] disabled:cursor-not-allowed disabled:opacity-50"
                                                    title="Regenerate images for this content"
                                                >
                                                    <VsxIcon
                                                        iconName="Refresh"
                                                        :size="16"
                                                        type="linear"
                                                    />
                                                    <span>Renew Images</span>
                                                </button>
                                            </template>
                                            <template v-else>
                                                <button
                                                    @click="
                                                        handleGenerateImage(
                                                            message,
                                                        )
                                                    "
                                                    :disabled="
                                                        isAnyMediaProcessActive
                                                    "
                                                    class="text-12 text-gray-600 hover:bg-pink-50 flex items-center gap-4 rounded-lg px-8 py-4 font-medium transition-colors hover:text-[#FF5EAB] disabled:cursor-not-allowed disabled:opacity-50"
                                                    title="Generate image for this content"
                                                >
                                                    <VsxIcon
                                                        iconName="Gallery"
                                                        :size="16"
                                                        type="linear"
                                                    />
                                                    <span>Generate Image</span>
                                                </button>
                                            </template>

                                            <template
                                                v-if="
                                                    hasCompletedMedia(
                                                        getMessageMedia(
                                                            message.id,
                                                        ),
                                                        'video',
                                                    )
                                                "
                                            >
                                                <button
                                                    @click="
                                                        handleRenewVideo(
                                                            message,
                                                        )
                                                    "
                                                    :disabled="
                                                        isAnyMediaProcessActive
                                                    "
                                                    class="text-12 text-gray-600 hover:bg-pink-50 flex items-center gap-4 rounded-lg px-8 py-4 font-medium transition-colors hover:text-[#FF5EAB] disabled:cursor-not-allowed disabled:opacity-50"
                                                    title="Regenerate videos for this content"
                                                >
                                                    <VsxIcon
                                                        iconName="Refresh"
                                                        :size="16"
                                                        type="linear"
                                                    />
                                                    <span>Renew Videos</span>
                                                </button>
                                            </template>
                                            <template v-else>
                                                <button
                                                    @click="
                                                        handleGenerateVideo(
                                                            message,
                                                        )
                                                    "
                                                    :disabled="
                                                        isAnyMediaProcessActive
                                                    "
                                                    class="text-12 text-gray-600 hover:bg-pink-50 flex items-center gap-4 rounded-lg px-8 py-4 font-medium transition-colors hover:text-[#FF5EAB] disabled:cursor-not-allowed disabled:opacity-50"
                                                    title="Generate video for this content"
                                                >
                                                    <VsxIcon
                                                        iconName="VideoPlay"
                                                        :size="16"
                                                        type="linear"
                                                    />
                                                    <span>Generate Video</span>
                                                </button>
                                            </template>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>

                    <!-- Enhanced Streaming indicator (Both modes) -->
                    <PlatformStreamingIndicator
                        v-if="isTabStreaming && activeTab === activeTabId"
                        :streamingContent="currentStreamingContent"
                        :platformId="activeTab"
                        :postCount="getPlatformPostCount(activeTab)"
                        :isInitialGeneration="isInitialContentGeneration"
                    />
                </div>

                <!-- Expanded Edit Interface (Both modes) -->
                <div v-if="expandedMessageId" class="mt-16 flex items-start">
                    <div
                        class="w-full rounded-[16px_4px_16px_16px] bg-[#F8F9FA] px-16 py-12"
                    >
                        <div class="mb-16 flex items-center justify-between">
                            <h4 class="text-gray-700 text-sm font-medium">
                                Edit Content
                            </h4>
                            <button
                                @click="closeExpandedEdit"
                                class="text-gray-400 hover:text-gray-600 transition-colors"
                            >
                                <VsxIcon
                                    iconName="CloseCircle"
                                    :size="16"
                                    type="linear"
                                />
                            </button>
                        </div>

                        <!-- Edit Prompt Input -->
                        <div class="space-y-16">
                            <textarea
                                v-model="editPrompt"
                                :disabled="isEditingMessage"
                                placeholder="How would you like to improve this content?"
                                class="bg-gray-50 w-full resize-none rounded-lg border-0 px-16 py-12 text-sm outline-none focus:bg-white disabled:opacity-50"
                                rows="2"
                            ></textarea>

                            <div class="flex items-center justify-between">
                                <Button
                                    color="action"
                                    size="sm"
                                    @click="handleGenerateImprovement"
                                    :disabled="
                                        !editPrompt.trim() || isEditingMessage
                                    "
                                >
                                    {{
                                        isEditingMessage
                                            ? "Improving..."
                                            : "Improve"
                                    }}
                                </Button>

                                <div
                                    v-if="isEditingMessage"
                                    class="flex items-center gap-8"
                                >
                                    <div
                                        class="h-12 w-12 animate-spin rounded-full border-b-2 border-[#FF5EAB]"
                                    ></div>
                                </div>
                            </div>
                        </div>

                        <!-- Improved Content Preview -->
                        <div
                            v-if="improvedContent && !isEditingMessage"
                            class="mt-16 space-y-12"
                        >
                            <div class="bg-gray-50 rounded-lg p-16">
                                <div
                                    v-if="improvedContent.htmlContent"
                                    class="text-gray-700 prose prose-sm mb-12 max-w-none"
                                    v-html="improvedContent.htmlContent"
                                ></div>
                                <p v-else class="text-gray-700 mb-12">
                                    {{ improvedContent.content }}
                                </p>

                                <!-- Apply button -->
                                <div class="flex justify-end">
                                    <Button
                                        size="sm"
                                        color="action"
                                        @click="handleApplyChanges"
                                    >
                                        Apply
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Chat Input -->
            <div class="flex-shrink-0 border-t border-[#DCDDDD] p-16">
                <div class="relative">
                    <textarea
                        ref="chatInputRef"
                        v-model="chatInput"
                        @keydown="handleKeyDown"
                        @input="autoResizeTextarea"
                        :disabled="isLoading || isTabStreaming"
                        :placeholder="inputPlaceholder"
                        title="💡 Tip: Press Enter for new line, Shift+Enter to send"
                        :class="[
                            'max-h-[200px] min-h-[48px] w-full resize-none rounded-lg border border-[#DCDDDD] px-16 py-12 text-sm leading-relaxed outline-none transition-all duration-200 focus:border-[#FF5EAB] focus:ring-0 disabled:opacity-50',
                            chatInput.trim() ? 'pr-48' : 'pr-16',
                        ]"
                        rows="1"
                    ></textarea>

                    <!-- Send Button - Only visible when there's content -->
                    <button
                        v-if="chatInput.trim()"
                        @click="sendMessage"
                        :disabled="isLoading || isTabStreaming"
                        class="absolute bottom-12 right-8 flex h-32 w-32 items-center justify-center rounded-full bg-[#FF5EAB] text-white shadow-sm transition-all duration-200 hover:bg-[#E54A96] disabled:cursor-not-allowed disabled:opacity-50"
                        title="Send message (Shift+Enter)"
                    >
                        <VsxIcon iconName="Send" :size="16" type="linear" />
                    </button>
                </div>
            </div>
        </div>

        <!-- Media Regeneration Modal (Both modes) -->
        <MediaRegenerationModal
            v-if="regenerationModal.isOpen"
            :isOpen="regenerationModal.isOpen"
            :mediaType="regenerationModal.mediaType"
            :originalPrompt="regenerationModal.originalPrompt"
            @close="closeRegenerationModal"
            @regenerate="handleMediaRegeneration"
        />
    </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, nextTick } from "vue";
import { router } from "@inertiajs/vue3";
import { useTabChat } from "@/composables/useTabChat";
import { useMediaGeneration } from "@/composables/useMediaGeneration";
import { useMediaRegeneration } from "@/composables/useMediaRegeneration";
import { VsxIcon } from "vue-iconsax";
import GeneratedMedia from "@/Components/MediaGeneration/GeneratedMedia.vue";
import MediaRegenerationModal from "@/Components/MediaGeneration/MediaRegenerationModal.vue";
import PlatformStreamingIndicator from "./PlatformStreamingIndicator.vue";
import PostCard from "./PostCard.vue";
import Showdown from "showdown";
import Button from "../Button/Button.vue";

const props = defineProps({
    // Common props
    platformsData: {
        type: Object,
        required: true,
    },
    socialUrl: {
        type: String,
        default: "",
    },
    mode: {
        type: String,
        required: true,
        validator: (value) => ["posts", "campaign"].includes(value),
    },

    // Posts mode props
    questionsData: {
        type: Object,
        default: () => ({}),
    },

    // Campaign mode props
    campaignData: {
        type: Object,
        default: () => ({}),
    },
    audienceData: {
        type: Object,
        default: () => ({}),
    },

    // UI Configuration props
    headerTitle: {
        type: String,
        default: "",
    },
    backButtonTitle: {
        type: String,
        default: "Back to platform selection",
    },
    scheduleButtonText: {
        type: String,
        default: "Schedule Post",
    },
    scheduleButtonTitle: {
        type: String,
        default: "Schedule your posts",
    },
    backRoute: {
        type: String,
        default: "/build/posts",
    },
    inputPlaceholder: {
        type: String,
        default: "Ask for specific content adjustments...",
    },
});

const emit = defineEmits(["content-generated"]);

// Tab chat composable
const {
    activeTabId,
    isStreaming: isTabStreaming,
    currentStreamingContent,
    getTabMessages,
    getTabGeneratedContent,
    generateTabContent,
    sendTabMessage,
    editTabMessage,
    updateTabMessage,
} = useTabChat();

// Local state
const activeTab = ref("");
const isLoading = ref(false);
const chatInput = ref("");
const chatContainer = ref(null);
const chatInputRef = ref(null);
const isRegenerating = ref(false);
const hasActiveMediaGeneration = ref(false);
const isAutoGenerating = ref(false);
const converter = new Showdown.Converter();

// Edit functionality state (Posts mode only)
const expandedMessageId = ref(null);
const editPrompt = ref("");
const improvedContent = ref(null);
const isEditingMessage = ref(false);

// Inline editing state (Posts mode only)
const editingMessageId = ref(null);
const editingContent = ref("");
const originalEditingContent = ref("");

// Media generation composables (used by both modes)
const {
    isGenerating,
    generateMidjourneyImage,
    generateKlingVideo,
    checkTaskStatus,
    cancelTask,
    clearError,
} = useMediaGeneration();

const {
    regenerationModal,
    hasCompletedMedia,
    openRegenerationModal,
    closeRegenerationModal,
    regenerateMedia,
} = useMediaRegeneration();

// Media storage for messages (used by both modes)
const messageMedia = ref(new Map());

// Additional media generation state
const activeTasks = ref(new Set());

// Platform configuration
const platformConfig = {
    twitter: { name: "Twitter", icon: "Hashtag", color: "#1DA1F2" },
    instagram: { name: "Instagram", icon: "Instagram", color: "#E4405F" },
    linkedin: { name: "LinkedIn", icon: "UserSquare", color: "#0077B5" },
    facebook: { name: "Facebook", icon: "Facebook", color: "#1877F2" },
    pinterest: { name: "Pinterest", icon: "Heart", color: "#BD081C" },
    tiktok: { name: "TikTok", icon: "VideoPlay", color: "#000000" },
    threads: { name: "Threads", icon: "MessageText", color: "#000000" },
    bluesky: { name: "Bluesky", icon: "Cloud", color: "#00A8E8" },
    youtube: { name: "YouTube", icon: "Youtube", color: "#FF0000" },
    blog: { name: "Blog", icon: "DocumentText1", color: "#6B7280" },
};

// Computed properties
const currentDate = computed(() => {
    const formatter = new Intl.DateTimeFormat("en-US", {
        month: "short",
        day: "numeric",
    });
    return formatter.format(new Date());
});

const headerSubtitle = computed(() => {
    return props.mode === "posts"
        ? "Generate content for this platform"
        : "Generate campaign content for this platform";
});

const contentTypeLabel = computed(() => {
    return props.mode === "posts" ? "content" : "Campaign Content";
});

const loadingMessage = computed(() => {
    if (isAutoGenerating.value) {
        return props.mode === "posts"
            ? "Generating content..."
            : "Generating campaign content...";
    }
    return "Initializing...";
});

const isAnyMediaProcessActive = computed(() => {
    if (props.mode !== "posts") return false;

    if (
        isGenerating.value ||
        isRegenerating.value ||
        hasActiveMediaGeneration.value
    )
        return true;

    for (const [, mediaArray] of messageMedia.value.entries()) {
        if (
            mediaArray.some(
                (media) =>
                    media.status === "pending" || media.status === "processing",
            )
        ) {
            return true;
        }
    }

    return false;
});

// Helper methods for streaming indicator
const getPlatformPostCount = (platformId) => {
    return props.platformsData[platformId] || 1;
};

const isInitialContentGeneration = computed(() => {
    // Check if this is the initial content generation (no messages yet)
    return getTabMessages(activeTab.value).length === 0 && isTabStreaming.value;
});

// Helper function for empty state messaging
const getEmptyStateMessage = () => {
    if (isAutoGenerating.value) {
        return props.mode === "posts"
            ? "Content is being generated automatically for all platforms..."
            : "Campaign content is being generated automatically for all platforms...";
    }
    if (isLoading.value) {
        return "Preparing to generate content...";
    }

    if (props.mode === "posts") {
        return "Content will be generated automatically when you visit this page";
    } else {
        const postCount = getPlatformPostCount(activeTab.value);
        return `I'll generate ${postCount} campaign post${postCount > 1 ? "s" : ""} for ${getPlatformName(activeTab.value)} based on your campaign data.`;
    }
};

// Helper function to get platform status
const getPlatformStatus = (platformId) => {
    const messages = getTabMessages(platformId);
    const hasContent = messages.some(
        (msg) => msg.role === "assistant" && msg.isInitialContent,
    );

    if (hasContent) {
        return "complete";
    }

    if (
        (isTabStreaming.value && activeTab.value === platformId) ||
        (isLoading.value && activeTab.value === platformId) ||
        isAutoGenerating.value
    ) {
        return "generating";
    }

    return "pending";
};

// Platform helper methods
const getPlatformName = (platformId) => {
    return (
        platformConfig[platformId]?.name ||
        platformId.charAt(0).toUpperCase() + platformId.slice(1)
    );
};

const getPlatformIcon = (platformId) => {
    return platformConfig[platformId]?.icon || "DocumentText1";
};

const getPlatformColor = (platformId) => {
    return platformConfig[platformId]?.color || "#6B7280";
};

// Tab management
const setActiveTab = (platformId) => {
    activeTab.value = platformId;
    activeTabId.value = platformId; // Sync with composable state

    // Auto-generate content if tab is empty
    if (getTabMessages(platformId).length === 0 && !isTabStreaming.value) {
        generateContentForPlatform(platformId);
    }
};

// Context building methods
const buildContextFromData = () => {
    if (props.mode === "campaign") {
        return buildContextFromCampaignData();
    } else {
        return buildContextFromPostsData();
    }
};

const buildContextFromPostsData = () => {
    let context =
        "Create engaging social media content that resonates with your audience. ";

    if (props.questionsData && Object.keys(props.questionsData).length > 0) {
        // Add questions data context
        Object.entries(props.questionsData).forEach(([key, value]) => {
            if (value) {
                context += `${key}: ${value}. `;
            }
        });
    }

    context +=
        "Focus on creating compelling, platform-appropriate content that drives engagement. ";
    return context;
};

const buildContextFromCampaignData = () => {
    let context =
        "Create engaging campaign content that drives action and awareness. ";

    if (props.campaignData && Object.keys(props.campaignData).length > 0) {
        const { campaignType, targetAmount, campaignDescription } =
            props.campaignData;
        context += `Campaign type: ${campaignType || "brand awareness campaign"}. `;

        if (targetAmount) {
            context += `Fundraising target: ${targetAmount}. `;
            context += `Focus on compelling calls-to-action that encourage donations and support. `;
        }

        if (campaignDescription) {
            context += `Campaign description: ${campaignDescription}. `;
        }

        context += `Ensure content aligns with campaign goals and messaging. `;
    }

    if (props.audienceData && Object.keys(props.audienceData).length > 0) {
        const { gender, ageRange, location, salary } = props.audienceData;
        context += `Target audience: `;

        if (gender) context += `${gender} gender, `;
        if (ageRange?.[0]) context += `age range: ${ageRange[0]}, `;
        if (location) context += `location: ${location}, `;
        if (salary) context += `income level: ${salary}, `;

        context += `tailor messaging and tone to resonate with this demographic. `;
    }

    context += `Use persuasive language, emotional appeals, and clear value propositions. `;
    context += `Include relevant hashtags and engagement-driving elements. `;

    return context;
};

// Content generation
const generateContentForPlatform = async (platformId) => {
    if (isLoading.value || isTabStreaming.value) return;

    isLoading.value = true;
    isAutoGenerating.value = true;

    try {
        const context = buildContextFromData();
        const postCount = getPlatformPostCount(platformId);
        const platformName = getPlatformName(platformId);

        // Enhanced context with platform-specific guidance
        let enhancedContext = `${context} `;

        if (props.mode === "campaign") {
            enhancedContext += `Create ${postCount} compelling campaign post${postCount > 1 ? "s" : ""} specifically optimized for ${platformName}. `;

            // Add platform-specific guidance for campaigns
            if (platformId === "instagram") {
                enhancedContext += `Use visual storytelling, relevant hashtags, and engaging captions that encourage interaction. `;
            } else if (platformId === "facebook") {
                enhancedContext += `Focus on community building, shareable content, and clear calls-to-action. `;
            } else if (platformId === "twitter") {
                enhancedContext += `Keep it concise, use trending hashtags, and encourage retweets and engagement. `;
            } else if (platformId === "linkedin") {
                enhancedContext += `Maintain professional tone while highlighting impact and credibility. `;
            } else if (platformId === "tiktok") {
                enhancedContext += `Create engaging, trend-aware content that encourages participation and sharing. `;
            }
        } else {
            enhancedContext += `Generate ${postCount} post${postCount > 1 ? "s" : ""} for ${platformName}. `;
        }

        if (postCount > 1) {
            enhancedContext += `Separate multiple posts with <------->. `;
            if (props.mode === "campaign") {
                enhancedContext += `Vary the messaging and approach for each post while maintaining campaign consistency. `;
            }
        }

        const payload = {
            context: enhancedContext,
            post_count: postCount,
            platform: platformId,
        };

        // Add mode-specific payload data
        if (props.mode === "campaign") {
            payload.feature = "campaign_content";
            payload.audience_gender =
                props.audienceData?.gender || "not specified";
            payload.audience_age =
                props.audienceData?.ageRange?.[0] || "not specified";
            payload.audience_income =
                props.audienceData?.salary || "not specified";
            payload.campaign_type =
                props.campaignData?.campaignType || "brand awareness campaign";
            payload.fundraising_target = props.campaignData?.targetAmount || "";
            payload.call_to_action =
                props.campaignData?.campaignDescription || "";
            payload.selected_social_platform = platformName;
        }

        const result = await generateTabContent(
            platformId,
            platformId,
            payload,
            props.mode === "campaign"
                ? props.audienceData
                : props.questionsData,
        );

        emit("content-generated", {
            platform: platformId,
            content: result.content,
            htmlContent: getTabGeneratedContent(platformId).htmlContent,
            postCount: postCount,
        });
    } catch (error) {
        console.error("Error generating content:", error);
    } finally {
        isLoading.value = false;
        isAutoGenerating.value = false;
    }
};

// Message handling
const sendMessage = async () => {
    if (!chatInput.value.trim() || isLoading.value || isTabStreaming.value)
        return;

    const message = chatInput.value.trim();
    chatInput.value = "";

    // Reset textarea height
    if (chatInputRef.value) {
        chatInputRef.value.style.height = "auto";
        chatInputRef.value.rows = 1;
    }

    try {
        const payload = {
            context: buildContextFromData(),
        };

        // Add mode-specific payload data
        if (props.mode === "campaign") {
            payload.feature = "campaign_content";
            payload.audience_gender =
                props.audienceData?.gender || "not specified";
            payload.audience_age =
                props.audienceData?.ageRange?.[0] || "not specified";
            payload.audience_income =
                props.audienceData?.salary || "not specified";
            payload.campaign_type =
                props.campaignData?.campaignType || "brand awareness campaign";
            payload.fundraising_target = props.campaignData?.targetAmount || "";
            payload.call_to_action =
                props.campaignData?.campaignDescription || "";
            payload.selected_social_platform = getPlatformName(activeTab.value);
            payload.campaignData = props.campaignData;
            payload.audienceData = props.audienceData;
        }

        await sendTabMessage(activeTab.value, message, payload);
    } catch (error) {
        console.error("Error sending message:", error);
    }
};

const handleKeyDown = (event) => {
    if (event.key === "Enter" && !event.shiftKey) {
        event.preventDefault();
        sendMessage();
    }
    // If Shift+Enter, do nothing special: the default behavior is to insert a newline
};

const autoResizeTextarea = () => {
    if (chatInputRef.value) {
        chatInputRef.value.style.height = "auto";
        chatInputRef.value.style.height =
            Math.min(chatInputRef.value.scrollHeight, 200) + "px";
    }
};

// Navigation handlers
const handleBackNavigation = () => {
    router.visit(props.backRoute);
};

const handleSchedulePost = () => {
    router.visit("/build/schedule");
};

// Content handling methods (used by both posts and campaign modes)
const getIndividualPosts = (content) => {
    // Handle multiple delimiter formats for flexibility
    const delimiters = ["<<------->>", "<<----->>", "<------->", "<------->"];
    let posts = [content]; // Default to single post if no delimiter found
    let usedDelimiter = null;

    // Try each delimiter to find the one that splits the content
    for (const delimiter of delimiters) {
        const splitPosts = content
            .split(delimiter)
            .filter((post) => post.trim().length > 0);
        if (splitPosts.length > 1) {
            posts = splitPosts;
            usedDelimiter = delimiter;
            console.log(
                `🔍 Found ${splitPosts.length} posts using delimiter: "${delimiter}"`,
            );
            break;
        }
    }

    if (!usedDelimiter) {
        console.log("📝 Single post detected (no delimiter found)");
    }

    return posts.map((post, index) => ({
        content: post.trim(),
        htmlContent: converter.makeHtml(post.trim()),
        isEdited: false,
        editedAt: null,
    }));
};

const getPostCount = (content) => {
    // Handle multiple delimiter formats for flexibility
    const delimiters = ["<<------->>", "<<----->>", "<------->", "<------->"];

    // Try each delimiter to find the one that splits the content
    for (const delimiter of delimiters) {
        const splitPosts = content
            .split(delimiter)
            .filter((post) => post.trim().length > 0);
        if (splitPosts.length > 1) {
            return splitPosts.length;
        }
    }

    // If no delimiter found, it's a single post
    return 1;
};

// Helper function to split content into posts using flexible delimiter detection
const splitContentIntoPosts = (content) => {
    const delimiters = ["<<------->>", "<<----->>", "<------->", "<------->"];

    // Try each delimiter to find the one that splits the content
    for (const delimiter of delimiters) {
        const splitPosts = content
            .split(delimiter)
            .filter((post) => post.trim().length > 0);
        if (splitPosts.length > 1) {
            return {
                posts: splitPosts.map((post) => post.trim()),
                delimiter: delimiter,
            };
        }
    }

    // If no delimiter found, return single post with default delimiter
    return {
        posts: [content.trim()],
        delimiter: "<------->",
    };
};

// Helper function to join posts back together
const joinPostsWithDelimiter = (posts, delimiter = "<------->") => {
    return posts.join(`\n\n${delimiter}\n\n`);
};

const getPostMedia = (messageId, postIndex) => {
    const mediaArray = messageMedia.value.get(messageId) || [];
    return mediaArray.filter((media) => media.postIndex === postIndex);
};

const getMessageMedia = (messageId) => {
    return messageMedia.value.get(messageId) || [];
};

const addMediaToMessage = (messageId, mediaItem) => {
    if (!messageMedia.value.has(messageId)) {
        messageMedia.value.set(messageId, []);
    }
    messageMedia.value.get(messageId).push(mediaItem);
};

// Media task polling
const pollTaskStatus = async (taskId) => {
    if (activeTasks.value.has(taskId)) return;

    activeTasks.value.add(taskId);

    const pollInterval = setInterval(async () => {
        try {
            const status = await checkTaskStatus(taskId);

            if (status.status === "completed" || status.status === "failed") {
                clearInterval(pollInterval);
                activeTasks.value.delete(taskId);
                hasActiveMediaGeneration.value = false;

                // Update media item status in storage
                for (const [
                    messageId,
                    mediaArray,
                ] of messageMedia.value.entries()) {
                    const mediaItem = mediaArray.find(
                        (item) => item.taskId === taskId,
                    );
                    if (mediaItem) {
                        mediaItem.status = status.status;
                        if (status.status === "completed") {
                            // Store the full status response so GeneratedMedia.vue can access output
                            mediaItem.result = status;
                        }
                        break;
                    }
                }
            }
        } catch (error) {
            console.error("Error polling task status:", error);
            clearInterval(pollInterval);
            activeTasks.value.delete(taskId);
            hasActiveMediaGeneration.value = false;
        }
    }, 3000); // Poll every 3 seconds
};

const extractTextFromMessage = (message) => {
    // Remove HTML tags and get plain text
    const tempDiv = document.createElement("div");
    tempDiv.innerHTML = message.htmlContent || message.content;
    return tempDiv.textContent || tempDiv.innerText || "";
};

// Inline editing methods (used by both posts and campaign modes)
const startInlineEdit = (message) => {
    editingMessageId.value = message.id;
    editingContent.value = message.content;
    originalEditingContent.value = message.content;
};

const saveInlineEdit = async () => {
    if (!editingMessageId.value) return;

    try {
        const htmlContent = converter.makeHtml(editingContent.value);

        // Update the message with the new content
        const updatedMessage = updateTabMessage(
            activeTab.value,
            editingMessageId.value,
            editingContent.value,
            htmlContent,
        );

        console.log("Message updated successfully:", updatedMessage);
        cancelInlineEdit();
    } catch (error) {
        console.error("Error saving edit:", error);
        // Revert to original content on error
        editingContent.value = originalEditingContent.value;
    }
};

const cancelInlineEdit = () => {
    editingMessageId.value = null;
    editingContent.value = "";
    originalEditingContent.value = "";
};

const handleInlineEditKeyDown = (event) => {
    if (event.key === "Enter" && event.ctrlKey) {
        event.preventDefault();
        saveInlineEdit();
    } else if (event.key === "Escape") {
        event.preventDefault();
        cancelInlineEdit();
    }
};

// Media generation methods (used by both posts and campaign modes)
const handleGenerateImage = (message) => {
    // Implementation would go here
    console.log("Generate image for message:", message.id);
};

const handleGenerateVideo = (message) => {
    // Implementation would go here
    console.log("Generate video for message:", message.id);
};

const handleRenewImage = (message) => {
    // Implementation would go here
    console.log("Renew image for message:", message.id);
};

const handleRenewVideo = (message) => {
    // Implementation would go here
    console.log("Renew video for message:", message.id);
};

const handleRefreshMedia = () => {
    // Refresh all media statuses for the current tab
    const messages = getTabMessages(activeTab.value);

    messages.forEach((message) => {
        const mediaArray = messageMedia.value.get(message.id) || [];
        mediaArray.forEach(async (mediaItem) => {
            if (
                mediaItem.status === "pending" ||
                mediaItem.status === "processing"
            ) {
                try {
                    const status = await checkTaskStatus(mediaItem.taskId);
                    mediaItem.status = status.status;
                    if (status.status === "completed") {
                        // Store the full status response so GeneratedMedia.vue can access output
                        mediaItem.result = status;
                    }
                } catch (error) {
                    console.error("Error refreshing media status:", error);
                }
            }
        });
    });
};

const handleCancelMedia = () => {
    // Cancel all active media generation tasks
    activeTasks.value.forEach(async (taskId) => {
        try {
            await cancelTask(taskId);
            activeTasks.value.delete(taskId);
        } catch (error) {
            console.error("Error canceling task:", error);
        }
    });

    hasActiveMediaGeneration.value = false;
};

const handleMediaRegeneration = async (regenerationData) => {
    if (isGenerating.value) return;

    isRegenerating.value = true;

    try {
        const replaceMediaCallback = (newMediaItem) => {
            const { messageId, postIndex } = regenerationData;
            const mediaArray = messageMedia.value.get(messageId) || [];

            // Remove old media for this post and type
            const filteredMedia = mediaArray.filter(
                (media) =>
                    !(
                        media.postIndex === postIndex &&
                        media.type === newMediaItem.type
                    ),
            );

            // Add new media
            newMediaItem.postIndex = postIndex;
            filteredMedia.push(newMediaItem);
            messageMedia.value.set(messageId, filteredMedia);
        };

        const pollStatusCallback = (taskId) => {
            pollTaskStatus(taskId);
        };

        await regenerateMedia(
            regenerationData,
            replaceMediaCallback,
            pollStatusCallback,
        );
        closeRegenerationModal();
    } catch (error) {
        console.error("Error regenerating media:", error);
    } finally {
        isRegenerating.value = false;
    }
};

// Post handling methods (used by both posts and campaign modes)
const handleUpdatePost = (messageId, postData) => {
    try {
        // Find the message and update the specific post
        const messages = getTabMessages(activeTab.value);
        const message = messages.find((m) => m.id === messageId);
        if (!message) return;

        // Split the content into individual posts using flexible delimiter detection
        const { posts, delimiter } = splitContentIntoPosts(message.content);

        // Update the specific post
        if (posts[postData.index]) {
            posts[postData.index] = postData.content;

            // Rejoin the posts using the same delimiter
            const updatedContent = joinPostsWithDelimiter(posts, delimiter);

            // Update the message
            updateTabMessage(
                activeTab.value,
                messageId,
                updatedContent,
                converter.makeHtml(updatedContent),
            );
        }
    } catch (error) {
        console.error("Error updating post:", error);
    }
};

const handleEditPost = (messageId, postIndex) => {
    try {
        const messages = getTabMessages(activeTab.value);
        const message = messages.find((m) => m.id === messageId);
        if (!message) return;

        // Open the expanded edit interface for this message
        openExpandedEdit(message);
    } catch (error) {
        console.error("Error opening edit interface:", error);
    }
};

const handleDuplicatePost = (messageId, postIndex) => {
    try {
        const messages = getTabMessages(activeTab.value);
        const message = messages.find((m) => m.id === messageId);
        if (!message) return;

        const { posts, delimiter } = splitContentIntoPosts(message.content);
        const postToDuplicate = posts[postIndex]?.trim();

        if (postToDuplicate) {
            // Insert the duplicated post after the original
            posts.splice(postIndex + 1, 0, postToDuplicate);
            const updatedContent = joinPostsWithDelimiter(posts, delimiter);

            updateTabMessage(
                activeTab.value,
                messageId,
                updatedContent,
                converter.makeHtml(updatedContent),
            );
        }
    } catch (error) {
        console.error("Error duplicating post:", error);
    }
};

const handleDeletePost = (messageId, postIndex) => {
    try {
        const messages = getTabMessages(activeTab.value);
        const message = messages.find((m) => m.id === messageId);
        if (!message) return;

        const { posts, delimiter } = splitContentIntoPosts(message.content);

        // Don't allow deleting if only one post remains
        if (posts.length <= 1) return;

        // Remove the post
        posts.splice(postIndex, 1);

        const updatedContent = joinPostsWithDelimiter(posts, delimiter);

        updateTabMessage(
            activeTab.value,
            messageId,
            updatedContent,
            converter.makeHtml(updatedContent),
        );
    } catch (error) {
        console.error("Error deleting post:", error);
    }
};

const handleGeneratePostImage = async (messageId, postIndex) => {
    if (isGenerating.value || hasActiveMediaGeneration.value) return;

    clearError();

    try {
        const messages = getTabMessages(activeTab.value);
        const message = messages.find((m) => m.id === messageId);
        if (!message) return;

        const { posts } = splitContentIntoPosts(message.content);
        const postContent = posts[postIndex]?.trim();

        if (!postContent) {
            alert("No content found for this post");
            return;
        }

        hasActiveMediaGeneration.value = true;

        const mediaItem = await generateMidjourneyImage(postContent, {
            aspect_ratio: "1:1",
        });

        // Add post index to media item for tracking
        mediaItem.postIndex = postIndex;

        addMediaToMessage(messageId, mediaItem);
        pollTaskStatus(mediaItem.taskId);
    } catch (error) {
        console.error("Error generating image for post:", error);
        hasActiveMediaGeneration.value = false;
    }
};

const handleGeneratePostVideo = async (messageId, postIndex) => {
    if (isGenerating.value || hasActiveMediaGeneration.value) return;

    clearError();

    try {
        const messages = getTabMessages(activeTab.value);
        const message = messages.find((m) => m.id === messageId);
        if (!message) return;

        const { posts } = splitContentIntoPosts(message.content);
        const postContent = posts[postIndex]?.trim();

        if (!postContent) {
            alert("No content found for this post");
            return;
        }

        hasActiveMediaGeneration.value = true;

        const mediaItem = await generateKlingVideo(postContent, {
            duration: 5,
            aspect_ratio: "16:9",
        });

        // Add post index to media item for tracking
        mediaItem.postIndex = postIndex;

        addMediaToMessage(messageId, mediaItem);
        pollTaskStatus(mediaItem.taskId);
    } catch (error) {
        console.error("Error generating video for post:", error);
        hasActiveMediaGeneration.value = false;
    }
};

const handleRenewPostImage = (messageId, postIndex) => {
    const messages = getTabMessages(activeTab.value);
    const message = messages.find((m) => m.id === messageId);
    if (!message) return;

    const { posts } = splitContentIntoPosts(message.content);
    const postContent = posts[postIndex]?.trim();

    if (!postContent) return;

    const currentMedia = getPostMedia(messageId, postIndex);
    const imageMedia = currentMedia.filter((media) => media.type === "image");

    openRegenerationModal({
        mediaType: "image",
        currentMedia: imageMedia,
        originalPostContent: postContent,
        messageId: messageId,
        postIndex: postIndex,
    });
};

const handleRenewPostVideo = (messageId, postIndex) => {
    const messages = getTabMessages(activeTab.value);
    const message = messages.find((m) => m.id === messageId);
    if (!message) return;

    const { posts } = splitContentIntoPosts(message.content);
    const postContent = posts[postIndex]?.trim();

    if (!postContent) return;

    const currentMedia = getPostMedia(messageId, postIndex);
    const videoMedia = currentMedia.filter((media) => media.type === "video");

    openRegenerationModal({
        mediaType: "video",
        currentMedia: videoMedia,
        originalPostContent: postContent,
        messageId: messageId,
        postIndex: postIndex,
    });
};

// Expanded edit functionality methods
const openExpandedEdit = (message) => {
    if (expandedMessageId.value) return; // Prevent multiple expansions

    // Reset local state before opening
    editPrompt.value = "";
    improvedContent.value = null;
    isEditingMessage.value = false;

    expandedMessageId.value = message.id;

    setTimeout(() => {
        scrollToBottom();
    }, 100);
};

const handleEditContent = (message) => {
    if (isEditingMessage.value || isLoading.value || isTabStreaming.value)
        return;

    openExpandedEdit(message);
};

const closeExpandedEdit = () => {
    if (isEditingMessage.value) {
        if (
            !confirm(
                "Content generation is in progress. Are you sure you want to close?",
            )
        ) {
            return;
        }
    }

    expandedMessageId.value = null;
    editPrompt.value = "";
    improvedContent.value = null;
    isEditingMessage.value = false;
};

const handleGenerateImprovement = async () => {
    if (!editPrompt.value.trim() || isEditingMessage.value) return;

    isEditingMessage.value = true;

    try {
        const message = getTabMessages(activeTab.value).find(
            (m) => m.id === expandedMessageId.value,
        );
        if (!message) return;

        const payload = {
            context: buildContextFromData(),
        };

        // Add mode-specific payload data
        if (props.mode === "campaign") {
            payload.feature = "campaign_content";
            payload.audience_gender =
                props.audienceData?.gender || "not specified";
            payload.audience_age =
                props.audienceData?.ageRange?.[0] || "not specified";
            payload.audience_income =
                props.audienceData?.salary || "not specified";
            payload.campaign_type =
                props.campaignData?.campaignType || "brand awareness campaign";
            payload.fundraising_target = props.campaignData?.targetAmount || "";
            payload.call_to_action =
                props.campaignData?.campaignDescription || "";
            payload.selected_social_platform = getPlatformName(activeTab.value);
        }

        const result = await editTabMessage(
            activeTab.value,
            expandedMessageId.value,
            editPrompt.value.trim(),
            payload,
        );

        improvedContent.value = {
            content: result.improvedContent,
            htmlContent: converter.makeHtml(result.improvedContent),
        };

        setTimeout(() => {
            scrollToBottom();
        }, 100);
    } catch (error) {
        console.error("Error generating improvement:", error);
        alert("Failed to generate improvement. Please try again.");
    } finally {
        isEditingMessage.value = false;
    }
};

const handleApplyChanges = () => {
    if (!improvedContent.value || !expandedMessageId.value) return;

    try {
        // Update the message with the improved content
        updateTabMessage(
            activeTab.value,
            expandedMessageId.value,
            improvedContent.value.content,
            improvedContent.value.htmlContent,
        );

        // Close the edit interface
        closeExpandedEdit();
    } catch (error) {
        console.error("Error applying changes:", error);
        alert("Failed to apply changes. Please try again.");
    }
};

// Automatic content generation for both posts and campaign modes
const startAutomaticContentGeneration = async () => {
    const platforms = Object.keys(props.platformsData);
    if (platforms.length === 0) return;

    isAutoGenerating.value = true;

    try {
        // Generate content for each platform sequentially
        for (const platformId of platforms) {
            // Check if platform already has content
            const existingMessages = getTabMessages(platformId);
            const hasContent = existingMessages.some(
                (msg) => msg.role === "assistant" && msg.isInitialContent,
            );

            if (!hasContent) {
                // Set active tab to show generation progress
                setActiveTab(platformId);

                // Generate content for this platform
                await generateContentForPlatform(platformId);

                // Small delay between platforms to show progress
                await new Promise((resolve) => setTimeout(resolve, 500));
            }
        }
    } catch (error) {
        console.error("Error during automatic content generation:", error);
    } finally {
        isAutoGenerating.value = false;
    }
};

// Scroll to bottom function
const scrollToBottom = (behavior = "smooth") => {
    if (chatContainer.value) {
        nextTick(() => {
            chatContainer.value.scrollTo({
                top: chatContainer.value.scrollHeight,
                behavior: behavior,
            });
        });
    }
};

// Initialize first tab and start automatic content generation
onMounted(() => {
    const platforms = Object.keys(props.platformsData);
    if (platforms.length > 0) {
        setActiveTab(platforms[0]);

        // Start automatic content generation for both posts and campaign modes
        startAutomaticContentGeneration();
    }
});

// Watch for platform changes
watch(
    () => props.platformsData,
    (newData) => {
        const platforms = Object.keys(newData);
        if (platforms.length > 0 && !activeTab.value) {
            setActiveTab(platforms[0]);
        }
    },
    { immediate: true },
);
</script>
